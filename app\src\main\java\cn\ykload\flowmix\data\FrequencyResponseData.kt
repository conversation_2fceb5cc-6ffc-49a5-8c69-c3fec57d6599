package cn.ykload.flowmix.data

/**
 * 数据源信息
 */
data class DataSource(
    val name: String,
    val displayName: String,
    val description: String
)

/**
 * 品牌信息
 */
data class Brand(
    val name: String
)

/**
 * 耳机信息
 */
data class Headphone(
    val fileName: String,
    val originalName: String,
    val lastUpdated: String,
    val sourceName: String? = null
)

/**
 * 测量条件的频响数据
 */
data class MeasurementCondition(
    val title: String,
    val frequencies: List<Float>? = null,
    val spl_values: List<Float>? = null // 匹配API返回的字段名
) {
    // 提供安全的访问方法
    fun getValidFrequencies(): List<Float> = frequencies ?: emptyList()
    fun getValidSplValues(): List<Float> = spl_values ?: emptyList()

    // 检查数据是否有效
    fun isValid(): Boolean {
        val freqs = getValidFrequencies()
        val spls = getValidSplValues()
        return freqs.isNotEmpty() && spls.isNotEmpty() && freqs.size == spls.size
    }
}

/**
 * 耳机的完整频响数据
 */
data class HeadphoneFrequencyData(
    val sourceName: String,
    val brandName: String,
    val headphoneName: String,
    val lastUpdated: String,
    val frequencyData: Map<String, MeasurementCondition>
)

/**
 * API响应基类
 */
data class ApiResponse<T>(
    val success: Boolean,
    val data: T?,
    val count: Int? = null,
    val sourceName: String? = null,
    val brandName: String? = null,
    val message: String
)

/**
 * 数据源列表响应
 */
typealias DataSourcesResponse = ApiResponse<List<DataSource>>

/**
 * 品牌列表响应
 */
typealias BrandsResponse = ApiResponse<List<String>>

/**
 * 耳机列表响应
 */
typealias HeadphonesResponse = ApiResponse<List<Headphone>>

/**
 * 频响数据响应
 */
typealias FrequencyDataResponse = ApiResponse<HeadphoneFrequencyData>

/**
 * 目标曲线信息
 */
data class TargetCurve(
    val fileName: String,
    val name: String,
    val lastUpdated: String,
    val measurementCount: Int
)

/**
 * 目标曲线数据
 */
data class TargetCurveData(
    val name: String,
    val lastUpdated: String,
    val frequencyData: Map<String, MeasurementCondition>
)

/**
 * 目标曲线列表响应
 */
typealias TargetCurvesResponse = ApiResponse<List<TargetCurve>>

/**
 * 目标曲线数据响应
 */
typealias TargetCurveDataResponse = ApiResponse<TargetCurveData>

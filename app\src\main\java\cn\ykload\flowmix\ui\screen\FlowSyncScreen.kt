package cn.ykload.flowmix.ui.screen

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Bluetooth
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.CloudOff
import androidx.compose.material.icons.filled.CloudSync
import androidx.compose.material.icons.filled.DevicesOther
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Headphones
import androidx.compose.material.icons.filled.Help
import androidx.compose.material.icons.filled.Key
import androidx.compose.material.icons.filled.Login
import androidx.compose.material.icons.filled.Logout
import androidx.compose.material.icons.filled.RadioButtonUnchecked
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Speaker
import androidx.compose.material.icons.filled.Sync
import androidx.compose.material.icons.filled.Tv
import androidx.compose.material.icons.filled.Usb
import androidx.compose.material.icons.filled.VolumeOff
import androidx.compose.material.icons.filled.VolumeUp
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import cn.ykload.flowmix.audio.AudioDeviceType
import cn.ykload.flowmix.audio.FlowMixAudioDeviceInfo
import cn.ykload.flowmix.data.CloudSyncStatus
import cn.ykload.flowmix.data.DeviceConfig
import cn.ykload.flowmix.ui.component.BottomModalType
import cn.ykload.flowmix.ui.component.BottomToast
import cn.ykload.flowmix.viewmodel.FlowSyncViewModel
import cn.ykload.flowmix.viewmodel.SyncStatus

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FlowSyncScreen(
    modifier: Modifier = Modifier,
    viewModel: FlowSyncViewModel = viewModel(),
    mainViewModel: cn.ykload.flowmix.viewmodel.MainViewModel? = null
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    var showAudioInfoDialog by remember { mutableStateOf(false) }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 页面标题
        Text(
            text = "FlowSync",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(vertical = 8.dp)
        )

        // 功能介绍卡片
        IntroductionCard()

        // 云端同步状态卡片
        CloudSyncStatusCard(
            isLoggedIn = uiState.isLoggedIn,
            cloudSyncStatus = uiState.cloudSyncStatus,
            onLogin = viewModel::showLoginDialog,
            onManualSync = viewModel::manualCloudSync,
            onLogout = viewModel::logout
        )

        // 当前设备状态卡片
        CurrentDeviceCard(
            currentDevice = uiState.currentDevice,
            currentConfig = uiState.currentDeviceConfig,
            syncStatus = uiState.syncStatus,
            isMonitoring = uiState.isDeviceMonitoring,
            onRefreshDevices = viewModel::refreshDeviceDetection
        )

        // 设备配置管理卡片
        DeviceConfigManagementCard(
            configuredDevices = uiState.configuredDevices,
            onConfigureDevice = viewModel::showDeviceConfigDialog
        )

        // 底部间距
        Spacer(modifier = Modifier.height(24.dp))
    }

    // 错误消息提示
    BottomToast(
        isVisible = uiState.errorMessage != null,
        message = uiState.errorMessage ?: "",
        type = BottomModalType.ERROR,
        onDismiss = viewModel::clearError
    )

    // 成功消息提示
    BottomToast(
        isVisible = uiState.successMessage != null,
        message = uiState.successMessage ?: "",
        type = BottomModalType.SUCCESS,
        onDismiss = viewModel::clearSuccess
    )

    // 音频状态信息对话框
    if (showAudioInfoDialog) {
        AudioInfoDialog(
            audioInfo = viewModel.getAudioStateInfo(),
            onDismiss = { showAudioInfoDialog = false }
        )
    }

    // 登录对话框
    if (uiState.showLoginDialog) {
        LoginDialog(
            onDismiss = viewModel::hideLoginDialog,
            onLoginSuccess = viewModel::hideLoginDialog
        )
    }
}

@Composable
private fun IntroductionCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        ),
        shape = RoundedCornerShape(25.dp)
    ) {
        Column(
            modifier = Modifier.padding(start = 20.dp, top = 12.dp, end = 20.dp, bottom = 20.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Sync,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(25.dp)
                )
                Text(
                    text = "同步播放设备",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            }

            Text(
                text = "借助 FlowSync 智能记忆，Flowmix 会识别并记住您的播放设备，自动加载对应配置，无需任何手动操作。",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onPrimaryContainer
            )

        }
    }
}

@Composable
private fun CurrentDeviceCard(
    currentDevice: FlowMixAudioDeviceInfo?,
    currentConfig: DeviceConfig?,
    syncStatus: SyncStatus,
    isMonitoring: Boolean,
    onRefreshDevices: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(25.dp)
    ) {
        Column(
            modifier = Modifier.padding(start = 20.dp, top = 12.dp, end = 20.dp, bottom = 20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "当前音频设备",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                // 刷新按钮
                IconButton(
                    onClick = onRefreshDevices,
                    modifier = Modifier.size(32.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "刷新设备检测",
                        modifier = Modifier.size(18.dp),
                        tint = MaterialTheme.colorScheme.primary
                    )
                }
            }

            if (currentDevice != null) {
                // 设备信息
                DeviceInfoRow(
                    device = currentDevice,
                    config = currentConfig
                )
            } else {
                // 无设备状态
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.VolumeOff,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "未检测到音频设备",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center
                    )
                    Text(
                        text = "请确保音频设备已连接并启动设备监听",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }
}

@Composable
private fun DeviceInfoRow(
    device: FlowMixAudioDeviceInfo,
    config: DeviceConfig?
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // 设备图标
        Icon(
            imageVector = getDeviceIcon(device.type),
            contentDescription = null,
            modifier = Modifier.size(32.dp),
            tint = MaterialTheme.colorScheme.primary
        )

        // 设备信息
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = device.name,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = device.type.displayName,
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun SyncStatusRow(syncStatus: SyncStatus) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "同步状态:",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 状态指示器
        when (syncStatus) {
            SyncStatus.SYNCING -> {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp
                )
            }
            else -> {
                Icon(
                    imageVector = when (syncStatus) {
                        SyncStatus.SYNCED -> Icons.Default.CheckCircle
                        SyncStatus.ERROR -> Icons.Default.Error
                        SyncStatus.DETECTING -> Icons.Default.Search
                        else -> Icons.Default.RadioButtonUnchecked
                    },
                    contentDescription = null,
                    modifier = Modifier.size(16.dp),
                    tint = when (syncStatus) {
                        SyncStatus.SYNCED -> Color.Green
                        SyncStatus.ERROR -> MaterialTheme.colorScheme.error
                        SyncStatus.DETECTING -> MaterialTheme.colorScheme.primary
                        else -> MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
            }
        }

        Text(
            text = syncStatus.displayName,
            style = MaterialTheme.typography.bodyMedium,
            color = when (syncStatus) {
                SyncStatus.SYNCED -> Color.Green
                SyncStatus.ERROR -> MaterialTheme.colorScheme.error
                SyncStatus.DETECTING -> MaterialTheme.colorScheme.primary
                else -> MaterialTheme.colorScheme.onSurfaceVariant
            }
        )
    }
}

@Composable
private fun ConfigurationStatusRow(config: DeviceConfig?) {
    Column(
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Text(
            text = "配置状态:",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // AutoEq配置状态
            ConfigStatusChip(
                label = "AutoEq",
                isConfigured = config?.autoEqConfig != null
            )

            // 频响配置状态
            ConfigStatusChip(
                label = "频响",
                isConfigured = config?.frequencyResponseConfig?.isComplete() == true
            )
        }
    }
}

@Composable
private fun ConfigStatusChip(
    label: String,
    isConfigured: Boolean
) {
    Surface(
        shape = RoundedCornerShape(25.dp),
        color = if (isConfigured) {
            Color.Green.copy(alpha = 0.1f)
        } else {
            MaterialTheme.colorScheme.surfaceVariant
        }
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Icon(
                imageVector = if (isConfigured) Icons.Default.Check else Icons.Default.Close,
                contentDescription = null,
                modifier = Modifier.size(12.dp),
                tint = if (isConfigured) Color.Green else MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall,
                color = if (isConfigured) Color.Green else MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun DeviceConfigManagementCard(
    configuredDevices: List<DeviceConfig>,
    onConfigureDevice: (FlowMixAudioDeviceInfo) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(25.dp)
    ) {
        Column(
            modifier = Modifier.padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "设备记忆",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            if (configuredDevices.isEmpty()) {
                // 空状态
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.DevicesOther,
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = "暂无已配置的设备",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center
                    )
                    Text(
                        text = "当您为设备保存 AutoEq 或频响配置时，设备将出现在这里",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center
                    )
                }
            } else {
                // 设备列表
                Text(
                    text = "已配置 ${configuredDevices.size} 个设备",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                configuredDevices.forEach { deviceConfig ->
                    DeviceConfigItem(
                        deviceConfig = deviceConfig
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DeviceConfigItem(
    deviceConfig: DeviceConfig
) {

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        shape = RoundedCornerShape(25.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 设备图标
            Icon(
                imageVector = getDeviceIconByType(deviceConfig.deviceType),
                contentDescription = null,
                modifier = Modifier.size(20.dp),
                tint = MaterialTheme.colorScheme.primary
            )

            // 设备信息
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = deviceConfig.deviceName,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = getDeviceTypeDisplayName(deviceConfig.deviceType),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}


/**
 * 根据设备类型字符串获取图标
 */
private fun getDeviceIconByType(deviceType: String) = when (deviceType) {
    "WIRED_HEADPHONES", "WIRED_HEADSET" -> Icons.Default.Headphones
    "BLUETOOTH_A2DP", "BLUETOOTH_SCO" -> Icons.Default.Bluetooth
    "USB_DEVICE", "USB_HEADSET" -> Icons.Default.Usb
    "BUILTIN_SPEAKER" -> Icons.Default.Speaker
    "HDMI" -> Icons.Default.Tv
    else -> Icons.Default.VolumeUp
}

/**
 * 根据设备类型字符串获取显示名称
 */
private fun getDeviceTypeDisplayName(deviceType: String) = when (deviceType) {
    "WIRED_HEADPHONES" -> "有线耳机"
    "WIRED_HEADSET" -> "有线耳麦"
    "BLUETOOTH_A2DP" -> "蓝牙耳机"
    "BLUETOOTH_SCO" -> "蓝牙通话"
    "USB_DEVICE" -> "USB音频"
    "USB_HEADSET" -> "USB耳麦"
    "BUILTIN_SPEAKER" -> "内置扬声器"
    "HDMI" -> "HDMI"
    else -> "未知设备"
}



/**
 * 获取设备类型对应的图标
 */
private fun getDeviceIcon(deviceType: AudioDeviceType) = when (deviceType) {
    AudioDeviceType.WIRED_HEADPHONES, AudioDeviceType.WIRED_HEADSET -> Icons.Default.Headphones
    AudioDeviceType.BLUETOOTH_A2DP, AudioDeviceType.BLUETOOTH_SCO -> Icons.Default.Bluetooth
    AudioDeviceType.USB_DEVICE, AudioDeviceType.USB_HEADSET -> Icons.Default.Usb
    AudioDeviceType.BUILTIN_SPEAKER -> Icons.Default.Speaker
    AudioDeviceType.HDMI -> Icons.Default.Tv
    else -> Icons.Default.VolumeUp
}

/**
 * 云端同步状态卡片
 */
@Composable
private fun CloudSyncStatusCard(
    isLoggedIn: Boolean,
    cloudSyncStatus: CloudSyncStatus,
    onLogin: () -> Unit,
    onManualSync: () -> Unit,
    onLogout: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(25.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (isLoggedIn) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(start = 20.dp, top = 12.dp, end = 20.dp, bottom = 20.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.CloudSync,
                    contentDescription = null,
                    tint = if (isLoggedIn) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.size(25.dp)
                )
                Text(
                    text = "跨设备同步",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.Bold,
                    color = if (isLoggedIn) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            if (isLoggedIn) {
                Text(
                    text = "借助 FlowSync 云，您的 Flowmix 配置将自动在所有登录了相同账号的设备间同步，确保配置一致，一切只为最佳的音频体验。",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )

                // 同步状态
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    CloudSyncStatusRow(cloudSyncStatus)

                    Row(
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 手动同步按钮
                        IconButton(
                            onClick = onManualSync,
                            enabled = cloudSyncStatus != CloudSyncStatus.SYNCING
                        ) {
                            Icon(
                                imageVector = Icons.Default.Sync,
                                contentDescription = "手动同步",
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }

                        // 登出按钮
                        IconButton(onClick = onLogout) {
                            Icon(
                                imageVector = Icons.Default.Logout,
                                contentDescription = "登出",
                                tint = MaterialTheme.colorScheme.error
                            )
                        }
                    }
                }
            } else {
                Text(
                    text = "登录 FlowSync 云，即可在多个手机/平板间同步您的音频设备配置。",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Button(
                    onClick = onLogin,
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(25.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Login,
                        contentDescription = null,
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("登录 FlowSync 云")
                }
            }
        }
    }
}

/**
 * 云端同步状态行
 */
@Composable
private fun CloudSyncStatusRow(syncStatus: CloudSyncStatus) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "同步状态:",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )

        // 状态指示器
        when (syncStatus) {
            CloudSyncStatus.SYNCING, CloudSyncStatus.CONNECTING -> {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp
                )
            }
            else -> {
                Icon(
                    imageVector = when (syncStatus) {
                        CloudSyncStatus.SYNCED -> Icons.Default.CheckCircle
                        CloudSyncStatus.ERROR -> Icons.Default.Error
                        CloudSyncStatus.OFFLINE -> Icons.Default.CloudOff
                        else -> Icons.Default.RadioButtonUnchecked
                    },
                    contentDescription = null,
                    modifier = Modifier.size(16.dp),
                    tint = when (syncStatus) {
                        CloudSyncStatus.SYNCED -> Color.Green
                        CloudSyncStatus.ERROR -> MaterialTheme.colorScheme.error
                        CloudSyncStatus.OFFLINE -> MaterialTheme.colorScheme.onSurfaceVariant
                        else -> MaterialTheme.colorScheme.primary
                    }
                )
            }
        }

        Text(
            text = when (syncStatus) {
                CloudSyncStatus.IDLE -> "空闲"
                CloudSyncStatus.CONNECTING -> "连接中"
                CloudSyncStatus.SYNCING -> "同步中"
                CloudSyncStatus.SYNCED -> "已同步"
                CloudSyncStatus.ERROR -> "错误"
                CloudSyncStatus.OFFLINE -> "离线"
            },
            style = MaterialTheme.typography.bodyMedium,
            color = when (syncStatus) {
                CloudSyncStatus.SYNCED -> Color.Green
                CloudSyncStatus.ERROR -> MaterialTheme.colorScheme.error
                CloudSyncStatus.OFFLINE -> MaterialTheme.colorScheme.onSurfaceVariant
                else -> MaterialTheme.colorScheme.primary
            }
        )
    }
}

/**
 * 登录对话框
 */
@Composable
private fun LoginDialog(
    onDismiss: () -> Unit,
    onLoginSuccess: () -> Unit
) {
    // 创建LoginViewModel实例
    val loginViewModel: cn.ykload.flowmix.viewmodel.LoginViewModel = viewModel()
    val loginUiState by loginViewModel.uiState.collectAsStateWithLifecycle()

    // 监听登录状态变化
    LaunchedEffect(loginUiState.isLoggedIn) {
        if (loginUiState.isLoggedIn) {
            onLoginSuccess()
        }
    }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(25.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                // 关闭按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    IconButton(
                        onClick = onDismiss,
                        modifier = Modifier.size(28.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Close,
                            contentDescription = "关闭",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.size(20.dp)
                        )
                    }
                }

                // 标题和图标
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(64.dp)
                            .background(
                                MaterialTheme.colorScheme.primaryContainer,
                                CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.CloudSync,
                            contentDescription = null,
                            modifier = Modifier.size(32.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }

                    Text(
                        text = "登录 FlowSync",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )

                    Text(
                        text = "输入6位数字登录码开启跨设备同步",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center
                    )
                }

                // 登录表单
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    OutlinedTextField(
                        value = loginUiState.loginCode,
                        onValueChange = { newValue ->
                            if (newValue.all { it.isDigit() } && newValue.length <= 6) {
                                loginViewModel.updateLoginCode(newValue)
                            }
                        },
                        label = { Text("登录码") },
                        placeholder = { Text("请输入6位数字") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        singleLine = true,
                        enabled = !loginUiState.isLoading,
                        modifier = Modifier.fillMaxWidth(),
                        shape = RoundedCornerShape(25.dp),
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Key,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                        },
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = MaterialTheme.colorScheme.primary,
                            focusedLabelColor = MaterialTheme.colorScheme.primary
                        )
                    )

                    // 错误消息
                    AnimatedVisibility(
                        visible = loginUiState.errorMessage != null,
                        enter = fadeIn() + expandVertically(),
                        exit = fadeOut() + shrinkVertically()
                    ) {
                        Card(
                            modifier = Modifier.fillMaxWidth(),
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.errorContainer
                            ),
                            shape = RoundedCornerShape(25.dp)
                        ) {
                            Row(
                                modifier = Modifier.padding(12.dp),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Error,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.error,
                                    modifier = Modifier.size(16.dp)
                                )
                                Text(
                                    text = loginUiState.errorMessage ?: "",
                                    style = MaterialTheme.typography.bodySmall,
                                    color = MaterialTheme.colorScheme.onErrorContainer
                                )
                            }
                        }
                    }

                    // 登录按钮
                    Button(
                        onClick = { loginViewModel.login() },
                        enabled = loginUiState.loginCode.length == 6 && !loginUiState.isLoading,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(48.dp),
                        shape = RoundedCornerShape(25.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary
                        )
                    ) {
                        if (loginUiState.isLoading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(20.dp),
                                strokeWidth = 2.dp,
                                color = MaterialTheme.colorScheme.onPrimary
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                "登录中...",
                                style = MaterialTheme.typography.titleMedium
                            )
                        } else {
                            Icon(
                                imageVector = Icons.Default.Login,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                "登录",
                                style = MaterialTheme.typography.titleMedium
                            )
                        }
                    }
                }

                // 帮助信息
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Help,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(14.dp)
                    )
                    Spacer(modifier = Modifier.width(6.dp))
                    Text(
                        text = "关注官方QQ群获取登录码",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * 音频状态信息对话框
 */
@Composable
private fun AudioInfoDialog(
    audioInfo: String,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "音频设备状态信息",
                style = MaterialTheme.typography.titleLarge
            )
        },
        text = {
            LazyColumn {
                item {
                    Text(
                        text = audioInfo,
                        style = MaterialTheme.typography.bodySmall,
                        fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace
                    )
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("关闭")
            }
        },
        modifier = Modifier.fillMaxWidth(0.9f)
    )
}

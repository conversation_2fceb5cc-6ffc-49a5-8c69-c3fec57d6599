package cn.ykload.flowmix.viewmodel

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import cn.ykload.flowmix.data.*
import cn.ykload.flowmix.network.NetworkManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import kotlinx.coroutines.Job

/**
 * 频响图表UI状态
 */
data class FrequencyResponseUiState(
    val isLoading: Boolean = false,
    val dataSources: List<DataSource> = emptyList(),
    val selectedDataSource: DataSource? = null,
    val brands: List<String> = emptyList(),
    val selectedBrand: String? = null,
    val headphones: List<Headphone> = emptyList(),
    val selectedHeadphone: Headphone? = null,
    val measurementConditions: List<String> = emptyList(),
    val selectedMeasurementCondition: String? = null,
    val frequencyData: HeadphoneFrequencyData? = null,
    val currentMeasurementData: MeasurementCondition? = null,
    val autoEqData: AutoEqData? = null,
    val targetCurves: List<TargetCurve> = emptyList(),
    val selectedTargetCurve: TargetCurve? = null,
    val targetCurveData: TargetCurveData? = null,
    val currentTargetData: MeasurementCondition? = null,
    val errorMessage: String? = null,
    val successMessage: String? = null,
    val isCompactMode: Boolean = false,
    val lastInteractionTime: Long = 0L,
    val isAnyDropdownExpanded: Boolean = false
) {
    val canLoadBrands: Boolean
        get() = selectedDataSource != null && !isLoading

    val canLoadHeadphones: Boolean
        get() = selectedDataSource != null && selectedBrand != null && !isLoading

    val canLoadFrequencyData: Boolean
        get() = selectedDataSource != null && selectedBrand != null && selectedHeadphone != null && !isLoading

    val canShowChart: Boolean
        get() = currentMeasurementData?.isValid() == true

    val isSelectionComplete: Boolean
        get() = selectedDataSource != null && selectedBrand != null &&
                selectedHeadphone != null && selectedMeasurementCondition != null
}

/**
 * 频响图表ViewModel
 */
class FrequencyResponseViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        private const val TAG = "FrequencyResponseViewModel"
        private const val PREFS_NAME = "flowmix_frequency_response"
        private const val KEY_SELECTED_DATA_SOURCE = "selected_data_source"
        private const val KEY_SELECTED_BRAND = "selected_brand"
        private const val KEY_SELECTED_HEADPHONE = "selected_headphone"
        private const val KEY_SELECTED_MEASUREMENT_CONDITION = "selected_measurement_condition"
        private const val KEY_SELECTED_TARGET_CURVE = "selected_target_curve"
    }

    private val _uiState = MutableStateFlow(FrequencyResponseUiState())
    val uiState: StateFlow<FrequencyResponseUiState> = _uiState.asStateFlow()

    private val api = NetworkManager.frequencyResponseApi
    private var compactModeJob: Job? = null

    // SharedPreferences for state persistence
    private val sharedPreferences: SharedPreferences =
        application.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    // FlowSync回调接口，用于自动保存配置
    private var flowSyncCallback: ((String?, String?, String?, String?, String?) -> Unit)? = null

    // 标志：是否已经尝试过恢复状态
    private var hasTriedRestore = false

    // 是否正在应用FlowSync配置（防止循环保存）
    private var isApplyingFlowSyncConfig = false

    // 恢复状态的标志
    private var isRestoringState = false

    init {
        loadDataSources()
        loadTargetCurves()
    }

    /**
     * 加载所有数据源
     */
    fun loadDataSources() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)

            try {
                val response = api.getDataSources()
                if (response.isSuccessful) {
                    val dataSourcesResponse = response.body()
                    if (dataSourcesResponse?.success == true && dataSourcesResponse.data != null) {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            dataSources = dataSourcesResponse.data
                        )

                        // 数据源加载完成后，尝试恢复状态
                        if (!hasTriedRestore) {
                            hasTriedRestore = true
                            restoreState()
                        }
                    } else {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            errorMessage = dataSourcesResponse?.message ?: "获取数据源列表失败"
                        )
                    }
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = "网络请求失败: ${response.code()}"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "加载数据源列表失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 选择数据源
     */
    fun selectDataSource(dataSource: DataSource) {
        _uiState.value = _uiState.value.copy(
            selectedDataSource = dataSource,
            brands = emptyList(),
            selectedBrand = null,
            headphones = emptyList(),
            selectedHeadphone = null,
            measurementConditions = emptyList(),
            selectedMeasurementCondition = null,
            frequencyData = null,
            currentMeasurementData = null
        )
        loadBrands(dataSource.name)
        recordInteraction()
        if (!isRestoringState) {
            saveState()
        }
    }

    /**
     * 加载指定数据源的所有品牌
     */
    fun loadBrands(sourceName: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)

            try {
                val response = api.getBrands(sourceName)
                if (response.isSuccessful) {
                    val brandsResponse = response.body()
                    if (brandsResponse?.success == true && brandsResponse.data != null) {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            brands = brandsResponse.data
                        )

                        // 如果正在恢复状态，尝试恢复品牌选择
                        if (isRestoringState) {
                            restoreBrandSelection()
                        }
                    } else {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            errorMessage = brandsResponse?.message ?: "获取品牌列表失败"
                        )
                    }
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = "网络请求失败: ${response.code()}"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "加载品牌列表失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 选择品牌
     */
    fun selectBrand(brand: String) {
        _uiState.value = _uiState.value.copy(
            selectedBrand = brand,
            headphones = emptyList(),
            selectedHeadphone = null,
            measurementConditions = emptyList(),
            selectedMeasurementCondition = null,
            frequencyData = null,
            currentMeasurementData = null
        )

        val dataSource = _uiState.value.selectedDataSource
        if (dataSource != null) {
            loadHeadphones(dataSource.name, brand)
        }
        recordInteraction()
        if (!isRestoringState) {
            saveState()
        }
    }

    /**
     * 加载指定数据源和品牌的耳机列表
     */
    private fun loadHeadphones(sourceName: String, brand: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)

            try {
                val response = api.getHeadphones(sourceName, brand)
                if (response.isSuccessful) {
                    val headphonesResponse = response.body()
                    if (headphonesResponse?.success == true && headphonesResponse.data != null) {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            headphones = headphonesResponse.data
                        )

                        // 如果正在恢复状态，尝试恢复耳机选择
                        if (isRestoringState) {
                            restoreHeadphoneSelection()
                        }
                    } else {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            errorMessage = headphonesResponse?.message ?: "获取耳机列表失败"
                        )
                    }
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = "网络请求失败: ${response.code()}"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "加载耳机列表失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 选择耳机
     */
    fun selectHeadphone(headphone: Headphone) {
        _uiState.value = _uiState.value.copy(
            selectedHeadphone = headphone,
            measurementConditions = emptyList(),
            selectedMeasurementCondition = null,
            frequencyData = null,
            currentMeasurementData = null
        )

        val dataSource = _uiState.value.selectedDataSource
        val brand = _uiState.value.selectedBrand
        if (dataSource != null && brand != null) {
            loadFrequencyData(dataSource.name, brand, headphone.fileName)
        }
        recordInteraction()
        if (!isRestoringState) {
            saveState()
        }
    }

    /**
     * 加载频响数据
     */
    private fun loadFrequencyData(sourceName: String, brand: String, headphoneName: String) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, errorMessage = null)

            try {
                val response = api.getFrequencyData(sourceName, brand, headphoneName)
                if (response.isSuccessful) {
                    val frequencyDataResponse = response.body()
                    if (frequencyDataResponse?.success == true && frequencyDataResponse.data != null) {
                        val data = frequencyDataResponse.data
                        val conditions = data.frequencyData.keys.toList()

                        // 添加调试日志
                        android.util.Log.d("FrequencyResponseVM", "Loaded frequency data with ${conditions.size} conditions")
                        conditions.forEach { condition ->
                            val measurementData = data.frequencyData[condition]
                            android.util.Log.d("FrequencyResponseVM", "Condition: $condition, frequencies: ${measurementData?.frequencies?.size}, spl_values: ${measurementData?.spl_values?.size}")
                        }

                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            frequencyData = data,
                            measurementConditions = conditions
                        )

                        // 如果正在恢复状态，尝试恢复测量条件选择，否则自动选择第一个
                        if (isRestoringState) {
                            restoreMeasurementConditionSelection()
                        } else if (conditions.isNotEmpty() && !isApplyingFlowSyncConfig) {
                            // 只有在不是应用FlowSync配置时才自动选择第一个测量条件
                            selectMeasurementCondition(conditions.first())
                        }
                    } else {
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            errorMessage = frequencyDataResponse?.message ?: "获取频响数据失败"
                        )
                    }
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        errorMessage = "网络请求失败: ${response.code()}"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "加载频响数据失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 选择测量条件
     */
    fun selectMeasurementCondition(condition: String) {
        val frequencyData = _uiState.value.frequencyData
        if (frequencyData != null) {
            val measurementData = frequencyData.frequencyData[condition]

            // 添加调试日志
            android.util.Log.d("FrequencyResponseVM", "Selected condition: $condition")
            android.util.Log.d("FrequencyResponseVM", "Measurement data valid: ${measurementData?.isValid()}")
            if (measurementData != null) {
                android.util.Log.d("FrequencyResponseVM", "Frequencies size: ${measurementData.getValidFrequencies().size}")
                android.util.Log.d("FrequencyResponseVM", "SPL values size: ${measurementData.getValidSplValues().size}")
            }

            _uiState.value = _uiState.value.copy(
                selectedMeasurementCondition = condition,
                currentMeasurementData = measurementData
            )
        }
        recordInteraction()
        if (!isRestoringState) {
            saveState()
            // 只有在不是应用FlowSync配置时才自动保存
            if (!isApplyingFlowSyncConfig) {
                autoSaveToFlowSync()
            }
        }
    }
    
    /**
     * 设置AutoEq数据
     */
    fun setAutoEqData(autoEqData: AutoEqData?) {
        _uiState.value = _uiState.value.copy(autoEqData = autoEqData)
    }
    
    /**
     * 加载目标曲线列表
     */
    fun loadTargetCurves() {
        viewModelScope.launch {
            // 目标曲线加载不显示loading状态，因为它是后台加载的
            try {
                val response = api.getTargetCurves()
                if (response.isSuccessful) {
                    val targetCurvesResponse = response.body()
                    if (targetCurvesResponse?.success == true && targetCurvesResponse.data != null) {
                        _uiState.value = _uiState.value.copy(
                            targetCurves = targetCurvesResponse.data
                        )
                    } else {
                        _uiState.value = _uiState.value.copy(
                            errorMessage = targetCurvesResponse?.message ?: "获取目标曲线列表失败"
                        )
                    }
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "网络请求失败: ${response.code()}"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "获取目标曲线列表失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 选择目标曲线
     */
    fun selectTargetCurve(targetCurve: TargetCurve?) {
        _uiState.value = _uiState.value.copy(
            selectedTargetCurve = targetCurve,
            targetCurveData = null,
            currentTargetData = null
        )

        if (targetCurve != null) {
            loadTargetCurveData(targetCurve.fileName)
        }
        recordInteraction()
        if (!isRestoringState) {
            saveState()
            // 只有在不是应用FlowSync配置时才自动保存
            if (!isApplyingFlowSyncConfig) {
                autoSaveToFlowSync()
            }
        }
    }

    /**
     * 加载目标曲线数据
     */
    private fun loadTargetCurveData(targetName: String) {
        viewModelScope.launch {
            // 目标曲线数据加载也不显示loading状态，因为它是可选的
            try {
                val response = api.getTargetCurveData(targetName)
                if (response.isSuccessful) {
                    val targetDataResponse = response.body()
                    if (targetDataResponse?.success == true && targetDataResponse.data != null) {
                        val data = targetDataResponse.data
                        _uiState.value = _uiState.value.copy(
                            targetCurveData = data
                        )

                        // 自动选择第一个测量条件
                        val firstCondition = data.frequencyData.keys.firstOrNull()
                        if (firstCondition != null) {
                            val measurementData = data.frequencyData[firstCondition]
                            _uiState.value = _uiState.value.copy(
                                currentTargetData = measurementData
                            )
                        }
                    } else {
                        _uiState.value = _uiState.value.copy(
                            errorMessage = targetDataResponse?.message ?: "获取目标曲线数据失败"
                        )
                    }
                } else {
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "网络请求失败: ${response.code()}"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "获取目标曲线数据失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 清除错误消息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    /**
     * 清除成功消息
     */
    fun clearSuccess() {
        _uiState.value = _uiState.value.copy(successMessage = null)
    }

    /**
     * 记录用户交互时间
     */
    fun recordInteraction() {
        val currentTime = System.currentTimeMillis()
        _uiState.value = _uiState.value.copy(
            lastInteractionTime = currentTime,
            isCompactMode = false
        )

        // 取消之前的定时器
        compactModeJob?.cancel()

        // 启动自动进入缩略模式的定时器
        startCompactModeTimer()
    }

    /**
     * 更新下拉菜单展开状态
     */
    fun updateDropdownState(isAnyExpanded: Boolean) {
        _uiState.value = _uiState.value.copy(isAnyDropdownExpanded = isAnyExpanded)

        if (isAnyExpanded) {
            // 如果有下拉菜单展开，取消定时器
            compactModeJob?.cancel()
        } else {
            // 如果所有下拉菜单都关闭，重新启动定时器
            startCompactModeTimer()
        }
    }

    /**
     * 启动自动进入缩略模式的定时器
     */
    private fun startCompactModeTimer() {
        // 只有在选择完成、显示图表、不在缩略模式、没有下拉菜单展开时才启动定时器
        if (_uiState.value.isSelectionComplete &&
            _uiState.value.canShowChart &&
            !_uiState.value.isCompactMode &&
            !_uiState.value.isAnyDropdownExpanded) {

            compactModeJob = viewModelScope.launch {
                delay(3000) // 3秒后
                // 再次检查条件，确保在延迟期间状态没有改变
                if (_uiState.value.isSelectionComplete &&
                    _uiState.value.canShowChart &&
                    !_uiState.value.isCompactMode &&
                    !_uiState.value.isAnyDropdownExpanded) {
                    _uiState.value = _uiState.value.copy(isCompactMode = true)
                }
            }
        }
    }

    /**
     * 切换到展开模式
     */
    fun expandCard() {
        compactModeJob?.cancel()
        _uiState.value = _uiState.value.copy(
            isCompactMode = false,
            lastInteractionTime = System.currentTimeMillis()
        )
        // 重新启动定时器
        startCompactModeTimer()
    }

    /**
     * 手动切换紧凑模式
     */
    fun toggleCompactMode() {
        val newCompactMode = !_uiState.value.isCompactMode
        _uiState.value = _uiState.value.copy(isCompactMode = newCompactMode)

        if (newCompactMode) {
            compactModeJob?.cancel()
        } else {
            recordInteraction()
        }
    }

    /**
     * 设置FlowSync回调，用于自动保存配置
     */
    fun setFlowSyncCallback(callback: (String?, String?, String?, String?, String?) -> Unit) {
        flowSyncCallback = callback
    }

    /**
     * 保存当前状态到SharedPreferences
     */
    private fun saveState() {
        try {
            val currentState = _uiState.value
            sharedPreferences.edit().apply {
                // 保存数据源
                currentState.selectedDataSource?.let { dataSource ->
                    putString(KEY_SELECTED_DATA_SOURCE, "${dataSource.name}|${dataSource.displayName}|${dataSource.description}")
                } ?: remove(KEY_SELECTED_DATA_SOURCE)

                // 保存品牌
                currentState.selectedBrand?.let { brand ->
                    putString(KEY_SELECTED_BRAND, brand)
                } ?: remove(KEY_SELECTED_BRAND)

                // 保存耳机
                currentState.selectedHeadphone?.let { headphone ->
                    putString(KEY_SELECTED_HEADPHONE, "${headphone.fileName}|${headphone.originalName}|${headphone.lastUpdated}|${headphone.sourceName ?: ""}")
                } ?: remove(KEY_SELECTED_HEADPHONE)

                // 保存测量条件
                currentState.selectedMeasurementCondition?.let { condition ->
                    putString(KEY_SELECTED_MEASUREMENT_CONDITION, condition)
                } ?: remove(KEY_SELECTED_MEASUREMENT_CONDITION)

                // 保存目标曲线
                currentState.selectedTargetCurve?.let { targetCurve ->
                    putString(KEY_SELECTED_TARGET_CURVE, "${targetCurve.fileName}|${targetCurve.name}|${targetCurve.lastUpdated}|${targetCurve.measurementCount}")
                } ?: remove(KEY_SELECTED_TARGET_CURVE)

                apply()
            }

            android.util.Log.d(TAG, "已保存频响页面状态")
        } catch (e: Exception) {
            android.util.Log.e(TAG, "保存状态失败", e)
        }
    }

    /**
     * 从SharedPreferences恢复状态
     */
    private fun restoreState() {
        try {
            android.util.Log.d(TAG, "开始恢复频响页面状态")
            isRestoringState = true

            // 恢复数据源
            val dataSourceStr = sharedPreferences.getString(KEY_SELECTED_DATA_SOURCE, null)
            android.util.Log.d(TAG, "保存的数据源: $dataSourceStr")

            if (dataSourceStr != null) {
                val parts = dataSourceStr.split("|")
                if (parts.size >= 3) {
                    val dataSource = DataSource(parts[0], parts[1], parts[2])
                    val availableDataSources = _uiState.value.dataSources
                    android.util.Log.d(TAG, "可用数据源数量: ${availableDataSources.size}")

                    val matchingDataSource = availableDataSources.find { it.name == dataSource.name }
                    if (matchingDataSource != null) {
                        android.util.Log.d(TAG, "找到匹配的数据源: ${matchingDataSource.name}")
                        selectDataSource(matchingDataSource)
                        // 后续的恢复将在数据加载完成后自动触发
                    } else {
                        android.util.Log.w(TAG, "未找到匹配的数据源: ${dataSource.name}")
                        isRestoringState = false
                    }
                } else {
                    android.util.Log.w(TAG, "数据源字符串格式不正确")
                    isRestoringState = false
                }
            } else {
                android.util.Log.d(TAG, "没有保存的数据源")
                isRestoringState = false
            }

            android.util.Log.d(TAG, "已完成频响页面状态恢复尝试")
        } catch (e: Exception) {
            android.util.Log.e(TAG, "恢复状态失败", e)
            isRestoringState = false
        }
    }

    /**
     * 恢复品牌选择
     */
    private fun restoreBrandSelection() {
        try {
            val brandStr = sharedPreferences.getString(KEY_SELECTED_BRAND, null)
            android.util.Log.d(TAG, "尝试恢复品牌: $brandStr")

            if (brandStr != null) {
                val availableBrands = _uiState.value.brands
                android.util.Log.d(TAG, "可用品牌: $availableBrands")

                if (availableBrands.contains(brandStr)) {
                    android.util.Log.d(TAG, "找到匹配的品牌: $brandStr")
                    selectBrand(brandStr)
                    // 后续的恢复将在耳机数据加载完成后自动触发
                } else {
                    android.util.Log.w(TAG, "未找到匹配的品牌: $brandStr")
                    isRestoringState = false
                }
            } else {
                android.util.Log.d(TAG, "没有保存的品牌")
                isRestoringState = false
            }
        } catch (e: Exception) {
            android.util.Log.e(TAG, "恢复品牌选择失败", e)
            isRestoringState = false
        }
    }

    /**
     * 恢复耳机选择
     */
    private fun restoreHeadphoneSelection() {
        try {
            val headphoneStr = sharedPreferences.getString(KEY_SELECTED_HEADPHONE, null)
            android.util.Log.d(TAG, "尝试恢复耳机: $headphoneStr")

            if (headphoneStr != null) {
                val parts = headphoneStr.split("|")
                if (parts.size >= 3) {
                    val headphone = Headphone(
                        fileName = parts[0],
                        originalName = parts[1],
                        lastUpdated = parts[2],
                        sourceName = if (parts.size > 3 && parts[3].isNotEmpty()) parts[3] else null
                    )
                    val availableHeadphones = _uiState.value.headphones
                    android.util.Log.d(TAG, "可用耳机数量: ${availableHeadphones.size}")

                    val matchingHeadphone = availableHeadphones.find { it.fileName == headphone.fileName }
                    if (matchingHeadphone != null) {
                        android.util.Log.d(TAG, "找到匹配的耳机: ${matchingHeadphone.originalName}")
                        selectHeadphone(matchingHeadphone)
                        // 后续的恢复将在频响数据加载完成后自动触发
                    } else {
                        android.util.Log.w(TAG, "未找到匹配的耳机: ${headphone.fileName}")
                        isRestoringState = false
                    }
                } else {
                    android.util.Log.w(TAG, "耳机字符串格式不正确")
                    isRestoringState = false
                }
            } else {
                android.util.Log.d(TAG, "没有保存的耳机")
                isRestoringState = false
            }
        } catch (e: Exception) {
            android.util.Log.e(TAG, "恢复耳机选择失败", e)
            isRestoringState = false
        }
    }

    /**
     * 恢复测量条件选择
     */
    private fun restoreMeasurementConditionSelection() {
        try {
            val conditionStr = sharedPreferences.getString(KEY_SELECTED_MEASUREMENT_CONDITION, null)
            android.util.Log.d(TAG, "尝试恢复测量条件: $conditionStr")

            if (conditionStr != null) {
                val availableConditions = _uiState.value.measurementConditions
                android.util.Log.d(TAG, "可用测量条件: $availableConditions")

                if (availableConditions.contains(conditionStr)) {
                    android.util.Log.d(TAG, "找到匹配的测量条件: $conditionStr")
                    selectMeasurementCondition(conditionStr)
                } else {
                    android.util.Log.w(TAG, "未找到匹配的测量条件: $conditionStr")
                }
            } else {
                android.util.Log.d(TAG, "没有保存的测量条件")
            }

            // 恢复目标曲线
            restoreTargetCurveSelection()

            // 恢复完成
            isRestoringState = false
            android.util.Log.d(TAG, "状态恢复完成")
        } catch (e: Exception) {
            android.util.Log.e(TAG, "恢复测量条件选择失败", e)
            isRestoringState = false
        }
    }

    /**
     * 恢复目标曲线选择
     */
    private fun restoreTargetCurveSelection() {
        try {
            val targetCurveStr = sharedPreferences.getString(KEY_SELECTED_TARGET_CURVE, null)
            android.util.Log.d(TAG, "尝试恢复目标曲线: $targetCurveStr")

            if (targetCurveStr != null) {
                val parts = targetCurveStr.split("|")
                if (parts.size >= 4) {
                    val targetCurve = TargetCurve(
                        fileName = parts[0],
                        name = parts[1],
                        lastUpdated = parts[2],
                        measurementCount = parts[3].toIntOrNull() ?: 0
                    )
                    val availableTargetCurves = _uiState.value.targetCurves
                    android.util.Log.d(TAG, "可用目标曲线数量: ${availableTargetCurves.size}")

                    val matchingTargetCurve = availableTargetCurves.find { it.fileName == targetCurve.fileName }
                    if (matchingTargetCurve != null) {
                        android.util.Log.d(TAG, "找到匹配的目标曲线: ${matchingTargetCurve.name}")
                        selectTargetCurve(matchingTargetCurve)
                    } else {
                        android.util.Log.w(TAG, "未找到匹配的目标曲线: ${targetCurve.fileName}")
                    }
                } else {
                    android.util.Log.w(TAG, "目标曲线字符串格式不正确")
                }
            } else {
                android.util.Log.d(TAG, "没有保存的目标曲线")
            }
        } catch (e: Exception) {
            android.util.Log.e(TAG, "恢复目标曲线选择失败", e)
        }
    }

    /**
     * 自动保存当前选择到FlowSync
     */
    private fun autoSaveToFlowSync() {
        // 防止在应用FlowSync配置时触发循环保存
        if (isApplyingFlowSyncConfig) {
            android.util.Log.d(TAG, "正在应用FlowSync配置，跳过自动保存")
            return
        }

        val currentState = _uiState.value
        if (currentState.isSelectionComplete) {
            android.util.Log.d(TAG, "自动保存频响配置到FlowSync")
            flowSyncCallback?.invoke(
                currentState.selectedDataSource?.name,
                currentState.selectedBrand,
                currentState.selectedHeadphone?.fileName,
                currentState.selectedMeasurementCondition,
                currentState.selectedTargetCurve?.name
            )
        }
    }

    /**
     * 重置所有配置（当检测到新设备时调用）
     */
    fun resetAllConfigs() {
        android.util.Log.d(TAG, "重置所有频响配置（检测到新设备）")

        // 重置UI状态到初始状态
        _uiState.value = FrequencyResponseUiState(
            dataSources = _uiState.value.dataSources,
            targetCurves = _uiState.value.targetCurves,
            isCompactMode = _uiState.value.isCompactMode
        )

        // 清除保存的状态
        clearState()

        android.util.Log.d(TAG, "频响配置已重置")
    }

    /**
     * 清除保存的状态
     */
    private fun clearState() {
        try {
            sharedPreferences.edit().apply {
                remove(KEY_SELECTED_DATA_SOURCE)
                remove(KEY_SELECTED_BRAND)
                remove(KEY_SELECTED_HEADPHONE)
                remove(KEY_SELECTED_MEASUREMENT_CONDITION)
                remove(KEY_SELECTED_TARGET_CURVE)
                apply()
            }
            android.util.Log.d(TAG, "已清除保存的频响状态")
        } catch (e: Exception) {
            android.util.Log.e(TAG, "清除状态失败", e)
        }
    }

    /**
     * 应用FlowSync配置（从外部调用）
     */
    fun applyFlowSyncConfig(
        dataSource: String?,
        brand: String?,
        headphone: String?,
        measurementCondition: String?,
        targetCurve: String?
    ) {
        viewModelScope.launch {
            try {
                android.util.Log.d(TAG, "应用FlowSync配置: $dataSource - $brand $headphone")

                // 检查当前配置是否已经一致
                val currentState = _uiState.value
                val isConfigSame = currentState.selectedDataSource?.name == dataSource &&
                        currentState.selectedBrand == brand &&
                        currentState.selectedHeadphone?.fileName == headphone &&
                        currentState.selectedMeasurementCondition == measurementCondition &&
                        currentState.selectedTargetCurve?.name == targetCurve

                if (isConfigSame) {
                    android.util.Log.d(TAG, "频响配置已一致，跳过重载")
                    return@launch
                }

                android.util.Log.d(TAG, "频响配置不一致，开始重载")
                android.util.Log.d(TAG, "当前: ${currentState.selectedDataSource?.name} - ${currentState.selectedBrand} ${currentState.selectedHeadphone?.fileName}")
                android.util.Log.d(TAG, "目标: $dataSource - $brand $headphone")

                // 设置标志，防止循环保存
                isApplyingFlowSyncConfig = true

                // 智能更新：只更新需要改变的配置项
                // 使用已有的currentState变量

                // 检查数据源是否需要更新
                if (dataSource != null && currentState.selectedDataSource?.name != dataSource) {
                    android.util.Log.d(TAG, "数据源需要更新: ${currentState.selectedDataSource?.name} -> $dataSource")
                    val availableDataSources = currentState.dataSources
                    val matchingDataSource = availableDataSources.find { it.name == dataSource }
                    if (matchingDataSource != null) {
                        selectDataSource(matchingDataSource)
                        delay(500) // 等待品牌加载
                    }
                } else {
                    android.util.Log.d(TAG, "数据源无需更新: $dataSource")
                }

                // 检查品牌是否需要更新
                if (brand != null && _uiState.value.selectedBrand != brand) {
                    android.util.Log.d(TAG, "品牌需要更新: ${_uiState.value.selectedBrand} -> $brand")
                    val availableBrands = _uiState.value.brands
                    if (availableBrands.contains(brand)) {
                        selectBrand(brand)
                        delay(500) // 等待耳机加载
                    }
                } else {
                    android.util.Log.d(TAG, "品牌无需更新: $brand")
                }

                // 检查耳机是否需要更新
                if (headphone != null && _uiState.value.selectedHeadphone?.fileName != headphone) {
                    android.util.Log.d(TAG, "耳机需要更新: ${_uiState.value.selectedHeadphone?.fileName} -> $headphone")
                    val availableHeadphones = _uiState.value.headphones
                    val matchingHeadphone = availableHeadphones.find { it.fileName == headphone }
                    if (matchingHeadphone != null) {
                        selectHeadphone(matchingHeadphone)
                        delay(500) // 等待测量条件加载
                    }
                } else {
                    android.util.Log.d(TAG, "耳机无需更新: $headphone")
                }

                // 检查测量条件是否需要更新
                if (measurementCondition != null && _uiState.value.selectedMeasurementCondition != measurementCondition) {
                    android.util.Log.d(TAG, "测量条件需要更新: ${_uiState.value.selectedMeasurementCondition} -> $measurementCondition")
                    val availableConditions = _uiState.value.measurementConditions
                    if (availableConditions.contains(measurementCondition)) {
                        selectMeasurementCondition(measurementCondition)
                        delay(500) // 等待目标曲线加载
                    }
                } else {
                    android.util.Log.d(TAG, "测量条件无需更新: $measurementCondition")
                }

                // 检查目标曲线是否需要更新
                if (targetCurve != null && _uiState.value.selectedTargetCurve?.name != targetCurve) {
                    android.util.Log.d(TAG, "目标曲线需要更新: ${_uiState.value.selectedTargetCurve?.name} -> $targetCurve")
                    val availableTargetCurves = _uiState.value.targetCurves
                    val matchingTargetCurve = availableTargetCurves.find { it.name == targetCurve }
                    if (matchingTargetCurve != null) {
                        selectTargetCurve(matchingTargetCurve)
                    }
                } else {
                    android.util.Log.d(TAG, "目标曲线无需更新: $targetCurve")
                }

            } catch (e: Exception) {
                android.util.Log.e(TAG, "应用FlowSync配置失败", e)
            } finally {
                // 清除标志，恢复正常的自动保存功能
                isApplyingFlowSyncConfig = false
                android.util.Log.d(TAG, "FlowSync配置应用完成，恢复自动保存")
            }
        }
    }
}

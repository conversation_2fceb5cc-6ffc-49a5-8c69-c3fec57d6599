package cn.ykload.flowmix.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import cn.ykload.flowmix.audio.AudioDeviceManager
import cn.ykload.flowmix.audio.FlowMixAudioDeviceInfo
import cn.ykload.flowmix.auth.AuthManager
import cn.ykload.flowmix.data.AutoEqData
import cn.ykload.flowmix.data.CloudSyncStatus
import cn.ykload.flowmix.data.DeviceConfig
import cn.ykload.flowmix.network.NetworkManager
import cn.ykload.flowmix.storage.DeviceConfigManager
import cn.ykload.flowmix.sync.CloudSyncManager
import cn.ykload.flowmix.sync.SyncCompletionCallback
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch

/**
 * FlowSync功能状态
 */
data class FlowSyncUiState(
    val isLoading: Boolean = false,
    val currentDevice: FlowMixAudioDeviceInfo? = null,
    val availableDevices: List<FlowMixAudioDeviceInfo> = emptyList(),
    val currentDeviceConfig: DeviceConfig? = null,
    val configuredDevices: List<DeviceConfig> = emptyList(),
    val isDeviceMonitoring: Boolean = false,
    val syncStatus: SyncStatus = SyncStatus.IDLE,
    val cloudSyncStatus: CloudSyncStatus = CloudSyncStatus.OFFLINE,
    val isLoggedIn: Boolean = false,
    val errorMessage: String? = null,
    val successMessage: String? = null,
    val showDeviceConfigDialog: Boolean = false,
    val selectedDeviceForConfig: FlowMixAudioDeviceInfo? = null,
    val showLoginDialog: Boolean = false
)

/**
 * 同步状态枚举
 */
enum class SyncStatus(val displayName: String) {
    IDLE("待机"),
    DETECTING("检测中"),
    SYNCING("同步中"),
    SYNCED("已同步"),
    ERROR("错误")
}

/**
 * 设备配置应用回调接口
 */
interface DeviceConfigApplyCallback {
    /**
     * 应用AutoEq配置
     */
    fun applyAutoEqConfig(autoEqData: AutoEqData, isLoudnessCompensationEnabled: Boolean, globalGain: Float)

    /**
     * 应用频响配置
     */
    fun applyFrequencyResponseConfig(
        dataSource: String?,
        brand: String?,
        headphone: String?,
        measurementCondition: String?,
        targetCurve: String?
    )

    /**
     * 重置所有配置（当检测到新设备时调用）
     */
    fun resetAllConfigs()
}

/**
 * 设备配置保存回调接口
 */
interface DeviceConfigSaveCallback {
    /**
     * 保存当前配置到当前设备
     */
    fun saveCurrentConfigToDevice(
        autoEqData: AutoEqData?,
        isLoudnessCompensationEnabled: Boolean,
        globalGain: Float
    )
}

/**
 * FlowSync ViewModel
 * 管理设备检测、配置存储和UI状态
 */
class FlowSyncViewModel(
    application: Application,
    private val configApplyCallback: DeviceConfigApplyCallback? = null
) : AndroidViewModel(application), DeviceConfigSaveCallback, SyncCompletionCallback {

    companion object {
        private const val TAG = "FlowSyncViewModel"
    }

    private val audioDeviceManager = AudioDeviceManager(application)
    private val deviceConfigManager = DeviceConfigManager(application)
    private val authManager = AuthManager.getInstance(application, NetworkManager.flowSyncApi)
    private val cloudSyncManager = CloudSyncManager(application, authManager, deviceConfigManager, viewModelScope, this)

    // UI状态
    private val _uiState = MutableStateFlow(FlowSyncUiState())
    val uiState: StateFlow<FlowSyncUiState> = _uiState.asStateFlow()

    // 防抖保存机制
    private var debounceSaveJob: Job? = null
    private val DEBOUNCE_DELAY_MS = 500L // 500ms防抖延迟

    // 记录上一个设备ID，用于检测设备变更
    private var previousDeviceId: String? = null

    init {
        // 监听设备变更和配置变更
        observeDeviceChanges()
        observeConfigChanges()
        observeCloudSyncStatus()

        // 设置获取当前音频设备ID的回调
        cloudSyncManager.setCurrentAudioDeviceIdCallback {
            _uiState.value.currentDevice?.getDeviceIdentifier()
        }

        // 自动开始设备监听
        startDeviceMonitoring()
    }

    /**
     * 开始设备监听
     */
    fun startDeviceMonitoring() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    syncStatus = SyncStatus.DETECTING
                )
                
                audioDeviceManager.startMonitoring()
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isDeviceMonitoring = true,
                    syncStatus = SyncStatus.IDLE
                )
                
                Log.d(TAG, "设备监听已启动")
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    syncStatus = SyncStatus.ERROR,
                    errorMessage = "启动设备监听失败: ${e.message}"
                )
                Log.e(TAG, "启动设备监听失败", e)
            }
        }
    }

    /**
     * 停止设备监听
     */
    fun stopDeviceMonitoring() {
        viewModelScope.launch {
            try {
                audioDeviceManager.stopMonitoring()

                _uiState.value = _uiState.value.copy(
                    isDeviceMonitoring = false,
                    syncStatus = SyncStatus.IDLE
                )

                Log.d(TAG, "设备监听已停止")
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "停止设备监听失败: ${e.message}"
                )
                Log.e(TAG, "停止设备监听失败", e)
            }
        }
    }

    /**
     * 手动刷新设备检测
     */
    fun refreshDeviceDetection() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    syncStatus = SyncStatus.DETECTING
                )

                audioDeviceManager.refreshDeviceDetection()

                _uiState.value = _uiState.value.copy(
                    syncStatus = SyncStatus.IDLE
                )

                Log.d(TAG, "设备检测已刷新")
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    syncStatus = SyncStatus.ERROR,
                    errorMessage = "刷新设备检测失败: ${e.message}"
                )
                Log.e(TAG, "刷新设备检测失败", e)
            }
        }
    }

    /**
     * 获取音频状态详细信息
     */
    fun getAudioStateInfo(): String {
        return audioDeviceManager.getCurrentAudioStateInfo()
    }

    /**
     * 保存当前设备的AutoEq配置
     */
    fun saveCurrentDeviceAutoEqConfig(
        autoEqData: AutoEqData,
        isLoudnessCompensationEnabled: Boolean = false,
        globalGain: Float = 0f
    ) {
        val currentDevice = _uiState.value.currentDevice
        if (currentDevice == null) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "没有检测到当前音频设备"
            )
            return
        }

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    syncStatus = SyncStatus.SYNCING
                )

                val success = deviceConfigManager.saveAutoEqConfig(
                    currentDevice,
                    autoEqData,
                    isLoudnessCompensationEnabled,
                    globalGain
                )

                if (success) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        syncStatus = SyncStatus.SYNCED
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        syncStatus = SyncStatus.ERROR,
                        errorMessage = "保存AutoEq配置失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    syncStatus = SyncStatus.ERROR,
                    errorMessage = "保存配置时发生错误: ${e.message}"
                )
                Log.e(TAG, "保存AutoEq配置失败", e)
            }
        }
    }

    /**
     * 保存当前设备的频响配置
     */
    fun saveCurrentDeviceFrequencyResponseConfig(
        dataSource: String?,
        brand: String?,
        headphone: String?,
        measurementCondition: String?,
        targetCurve: String?
    ) {
        val currentDevice = _uiState.value.currentDevice
        if (currentDevice == null) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "没有检测到当前音频设备"
            )
            return
        }

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    syncStatus = SyncStatus.SYNCING
                )

                val success = deviceConfigManager.saveFrequencyResponseConfig(
                    currentDevice,
                    dataSource,
                    brand,
                    headphone,
                    measurementCondition,
                    targetCurve
                )

                if (success) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        syncStatus = SyncStatus.SYNCED
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        syncStatus = SyncStatus.ERROR,
                        errorMessage = "保存频响配置失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    syncStatus = SyncStatus.ERROR,
                    errorMessage = "保存配置时发生错误: ${e.message}"
                )
                Log.e(TAG, "保存频响配置失败", e)
            }
        }
    }



    /**
     * 显示设备配置对话框
     */
    fun showDeviceConfigDialog(device: FlowMixAudioDeviceInfo) {
        _uiState.value = _uiState.value.copy(
            showDeviceConfigDialog = true,
            selectedDeviceForConfig = device
        )
    }

    /**
     * 隐藏设备配置对话框
     */
    fun hideDeviceConfigDialog() {
        _uiState.value = _uiState.value.copy(
            showDeviceConfigDialog = false,
            selectedDeviceForConfig = null
        )
    }

    /**
     * 清除错误消息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }

    /**
     * 清除成功消息
     */
    fun clearSuccess() {
        _uiState.value = _uiState.value.copy(successMessage = null)
    }

    /**
     * 获取当前设备配置
     */
    fun getCurrentDeviceConfig(): DeviceConfig? {
        return _uiState.value.currentDeviceConfig
    }

    /**
     * 从外部保存当前设备的配置（用于与MainViewModel集成）
     */
    fun saveCurrentDeviceConfigFromExternal(
        autoEqData: AutoEqData? = null,
        isLoudnessCompensationEnabled: Boolean = false,
        globalGain: Float = 0f,
        dataSource: String? = null,
        brand: String? = null,
        headphone: String? = null,
        measurementCondition: String? = null,
        targetCurve: String? = null
    ) {
        val currentDevice = _uiState.value.currentDevice
        if (currentDevice == null) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "没有检测到当前音频设备"
            )
            return
        }

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    syncStatus = SyncStatus.SYNCING
                )

                var success = true
                val messages = mutableListOf<String>()

                // 保存AutoEq配置
                if (autoEqData != null) {
                    val autoEqSuccess = deviceConfigManager.saveAutoEqConfig(
                        currentDevice,
                        autoEqData,
                        isLoudnessCompensationEnabled,
                        globalGain
                    )
                    if (autoEqSuccess) {
                        messages.add("AutoEq配置")
                    } else {
                        success = false
                    }
                }

                // 保存频响配置
                if (dataSource != null || brand != null || headphone != null ||
                    measurementCondition != null || targetCurve != null) {
                    val freqSuccess = deviceConfigManager.saveFrequencyResponseConfig(
                        currentDevice,
                        dataSource,
                        brand,
                        headphone,
                        measurementCondition,
                        targetCurve
                    )
                    if (freqSuccess) {
                        messages.add("频响配置")
                    } else {
                        success = false
                    }
                }

                if (success && messages.isNotEmpty()) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        syncStatus = SyncStatus.SYNCED
                    )
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        syncStatus = SyncStatus.ERROR,
                        errorMessage = if (messages.isEmpty()) "没有配置需要保存" else "保存配置失败"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    syncStatus = SyncStatus.ERROR,
                    errorMessage = "保存配置时发生错误: ${e.message}"
                )
                Log.e(TAG, "保存外部配置失败", e)
            }
        }
    }

    /**
     * 监听设备变更
     */
    private fun observeDeviceChanges() {
        viewModelScope.launch {
            combine(
                audioDeviceManager.currentAudioDevice,
                audioDeviceManager.availableAudioDevices
            ) { currentDevice, availableDevices ->
                Pair(currentDevice, availableDevices)
            }.collect { (currentDevice, availableDevices) ->
                val currentDeviceId = currentDevice?.getDeviceIdentifier()

                // 检测设备是否发生变更
                val isDeviceChanged = currentDeviceId != previousDeviceId

                // 更新当前设备ID
                if (currentDevice != null) {
                    deviceConfigManager.setCurrentDeviceId(currentDeviceId!!)
                }

                // 获取当前设备配置
                val currentConfig = if (currentDevice != null) {
                    deviceConfigManager.getDeviceConfig(currentDevice)
                } else {
                    null
                }

                _uiState.value = _uiState.value.copy(
                    currentDevice = currentDevice,
                    availableDevices = availableDevices,
                    currentDeviceConfig = currentConfig
                )

                // 处理设备变更逻辑
                if (isDeviceChanged && configApplyCallback != null) {
                    handleDeviceChange(currentDevice, currentConfig)
                }

                // 更新上一个设备ID
                previousDeviceId = currentDeviceId

                Log.d(TAG, "设备状态更新: 当前设备=${currentDevice?.name}, 可用设备=${availableDevices.size}个, 设备变更=$isDeviceChanged")
            }
        }
    }

    /**
     * 监听配置变更
     */
    private fun observeConfigChanges() {
        viewModelScope.launch {
            deviceConfigManager.deviceConfigs.collect { configCollection ->
                val configuredDevices = configCollection.getConfiguredDevices()

                // 更新当前设备配置（如果当前设备存在）
                val currentDevice = _uiState.value.currentDevice
                val currentDeviceConfig = if (currentDevice != null) {
                    configCollection.getConfig(currentDevice.getDeviceIdentifier())
                } else {
                    null
                }

                _uiState.value = _uiState.value.copy(
                    configuredDevices = configuredDevices,
                    currentDeviceConfig = currentDeviceConfig
                )

                Log.d(TAG, "配置状态更新: 已配置设备=${configCollection.configs.size}个, 当前设备配置=${currentDeviceConfig != null}")
            }
        }
    }

    /**
     * 监听云端同步状态
     */
    private fun observeCloudSyncStatus() {
        viewModelScope.launch {
            combine(
                authManager.isLoggedIn,
                cloudSyncManager.syncStatus,
                cloudSyncManager.errorMessage,
                cloudSyncManager.getConnectionState()
            ) { isLoggedIn, syncStatus, errorMessage, connectionState ->
                _uiState.value = _uiState.value.copy(
                    isLoggedIn = isLoggedIn,
                    cloudSyncStatus = syncStatus
                )

                // 如果有云端同步错误，显示错误消息
                if (errorMessage != null) {
                    _uiState.value = _uiState.value.copy(errorMessage = errorMessage)
                    cloudSyncManager.clearError()
                }

                // 记录连接状态变化
                Log.d(TAG, "云端同步状态: $syncStatus, WebSocket状态: $connectionState")
            }.collect { }
        }
    }

    /**
     * 处理设备变更
     * 如果是新设备（没有配置），则重置所有配置
     * 如果是已知设备（有配置），则应用对应配置
     */
    private fun handleDeviceChange(
        currentDevice: FlowMixAudioDeviceInfo?,
        currentConfig: DeviceConfig?
    ) {
        viewModelScope.launch {
            try {
                if (currentDevice == null) {
                    Log.d(TAG, "没有检测到设备，重置所有配置")
                    configApplyCallback?.resetAllConfigs()
                    _uiState.value = _uiState.value.copy(syncStatus = SyncStatus.IDLE)
                    return@launch
                }

                if (currentConfig == null) {
                    // 新设备：没有保存的配置，重置所有配置
                    Log.d(TAG, "检测到新设备: ${currentDevice.name}，重置所有配置")
                    configApplyCallback?.resetAllConfigs()
                    _uiState.value = _uiState.value.copy(
                        syncStatus = SyncStatus.IDLE
                    )
                } else {
                    // 已知设备：有保存的配置，自动应用
                    Log.d(TAG, "检测到已知设备: ${currentDevice.name}，应用保存的配置")
                    autoApplyDeviceConfig(currentConfig)
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理设备变更失败", e)
                _uiState.value = _uiState.value.copy(
                    syncStatus = SyncStatus.ERROR,
                    errorMessage = "处理设备变更失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 自动应用设备配置
     */
    private fun autoApplyDeviceConfig(deviceConfig: DeviceConfig) {
        viewModelScope.launch {
            try {
                Log.d(TAG, "自动应用设备配置: ${deviceConfig.deviceName}")

                // 应用AutoEq配置
                deviceConfig.autoEqConfig?.let { autoEqConfig ->
                    val autoEqData = autoEqConfig.toAutoEqData()
                    configApplyCallback?.applyAutoEqConfig(
                        autoEqData,
                        autoEqConfig.isLoudnessCompensationEnabled,
                        autoEqConfig.globalGain
                    )
                    Log.d(TAG, "已应用AutoEq配置: ${autoEqConfig.name}")
                }

                // 应用频响配置
                deviceConfig.frequencyResponseConfig?.let { freqConfig ->
                    if (freqConfig.isComplete()) {
                        configApplyCallback?.applyFrequencyResponseConfig(
                            freqConfig.dataSource,
                            freqConfig.brand,
                            freqConfig.headphone,
                            freqConfig.measurementCondition,
                            freqConfig.targetCurve
                        )
                        Log.d(TAG, "已应用频响配置: ${freqConfig.getDisplayName()}")
                    }
                }

                _uiState.value = _uiState.value.copy(
                    syncStatus = SyncStatus.SYNCED
                )
            } catch (e: Exception) {
                Log.e(TAG, "自动应用设备配置失败", e)
                _uiState.value = _uiState.value.copy(
                    syncStatus = SyncStatus.ERROR,
                    errorMessage = "自动应用配置失败: ${e.message}"
                )
            }
        }
    }

    // DeviceConfigSaveCallback 接口实现
    override fun saveCurrentConfigToDevice(
        autoEqData: AutoEqData?,
        isLoudnessCompensationEnabled: Boolean,
        globalGain: Float
    ) {
        if (autoEqData != null) {
            // 使用防抖机制避免频繁保存
            debounceSaveConfigToDevice(
                autoEqData = autoEqData,
                isLoudnessCompensationEnabled = isLoudnessCompensationEnabled,
                globalGain = globalGain
            )
        }
    }

    // 云端同步相关方法

    /**
     * 显示登录对话框
     */
    fun showLoginDialog() {
        _uiState.value = _uiState.value.copy(showLoginDialog = true)
    }

    /**
     * 隐藏登录对话框
     */
    fun hideLoginDialog() {
        _uiState.value = _uiState.value.copy(showLoginDialog = false)
    }

    /**
     * 手动触发云端同步
     */
    fun manualCloudSync() {
        viewModelScope.launch {
            try {
                cloudSyncManager.manualSync()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "手动同步失败: ${e.message}"
                )
                Log.e(TAG, "手动同步失败", e)
            }
        }
    }

    /**
     * 登出
     */
    fun logout() {
        viewModelScope.launch {
            try {
                authManager.logout()
                _uiState.value = _uiState.value.copy(
                    successMessage = "已退出登录"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    errorMessage = "退出登录失败: ${e.message}"
                )
                Log.e(TAG, "退出登录失败", e)
            }
        }
    }

    /**
     * 获取同步状态信息（用于调试）
     */
    fun getSyncStatusInfo(): String {
        return cloudSyncManager.getSyncStatusInfo()
    }

    /**
     * 重置同步状态（用于调试和故障恢复）
     */
    fun resetSyncState() {
        cloudSyncManager.resetSyncState()
        _uiState.value = _uiState.value.copy(
            syncStatus = SyncStatus.IDLE,
            errorMessage = null
        )
    }

    // SyncCompletionCallback 接口实现

    /**
     * 同步完成后的回调
     * 如果配置有更新，则立即应用当前设备的配置
     */
    override fun onSyncCompleted(isSuccess: Boolean, isConfigUpdated: Boolean) {
        Log.d(TAG, "云端同步完成: success=$isSuccess, configUpdated=$isConfigUpdated")

        if (isSuccess && isConfigUpdated) {
            viewModelScope.launch {
                try {
                    // 获取当前播放设备
                    val currentDevice = _uiState.value.currentDevice
                    if (currentDevice != null) {
                        Log.d(TAG, "同步完成后应用当前设备配置: ${currentDevice.name}")

                        // 查找当前设备的配置
                        val deviceConfig = deviceConfigManager.getDeviceConfig(currentDevice.getDeviceIdentifier())
                        if (deviceConfig != null) {
                            Log.d(TAG, "找到当前设备配置，开始应用")

                            // 使用现有的自动应用方法
                            autoApplyDeviceConfig(deviceConfig)

                        } else {
                            Log.d(TAG, "当前设备没有配置，重置所有配置")
                            // 如果当前设备没有配置（可能被其他设备删除），重置配置
                            configApplyCallback?.resetAllConfigs()
                        }
                    } else {
                        Log.d(TAG, "当前没有播放设备，无需应用配置")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "同步完成后应用配置失败", e)
                    _uiState.value = _uiState.value.copy(
                        errorMessage = "应用同步配置失败: ${e.message}"
                    )
                }
            }
        } else if (isSuccess) {
            Log.d(TAG, "同步完成但配置无更新，无需重新应用")
        } else {
            Log.e(TAG, "云端同步失败")
        }
    }

    /**
     * 防抖保存配置到设备
     * 避免用户快速调节时频繁保存
     */
    private fun debounceSaveConfigToDevice(
        autoEqData: AutoEqData,
        isLoudnessCompensationEnabled: Boolean,
        globalGain: Float
    ) {
        // 取消之前的保存任务
        debounceSaveJob?.cancel()

        // 启动新的防抖保存任务
        debounceSaveJob = viewModelScope.launch {
            delay(DEBOUNCE_DELAY_MS)

            // 执行实际的保存操作
            saveCurrentDeviceConfigFromExternal(
                autoEqData = autoEqData,
                isLoudnessCompensationEnabled = isLoudnessCompensationEnabled,
                globalGain = globalGain
            )

            Log.d(TAG, "防抖保存完成: ${autoEqData.name}")
        }
    }

    override fun onCleared() {
        super.onCleared()
        // 停止设备监听
        audioDeviceManager.stopMonitoring()
    }
}

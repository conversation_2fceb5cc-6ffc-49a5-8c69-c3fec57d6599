package cn.ykload.flowmix.sync

import android.util.Log
import cn.ykload.flowmix.data.*
import cn.ykload.flowmix.network.NetworkManager
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import okhttp3.*
import java.util.concurrent.TimeUnit

/**
 * WebSocket 连接管理器
 * 负责与FlowSync后端建立和维护WebSocket连接
 */
class WebSocketManager(
    private val scope: CoroutineScope
) {
    
    companion object {
        private const val TAG = "WebSocketManager"
        private const val WS_URL = "wss://flowsync.ykload.com/ws"
        private const val PING_INTERVAL = 30_000L // 30秒心跳间隔
        private const val RECONNECT_DELAY = 5_000L // 5秒重连延迟
        private const val MAX_RECONNECT_ATTEMPTS = 5
    }
    
    private val gson = Gson()
    private val client = NetworkManager.getWebSocketClient()
    
    // WebSocket连接
    private var webSocket: WebSocket? = null
    private var pingJob: Job? = null
    private var reconnectJob: Job? = null
    private var reconnectAttempts = 0
    
    // 连接状态
    private val _connectionState = MutableStateFlow(WebSocketState.DISCONNECTED)
    val connectionState: StateFlow<WebSocketState> = _connectionState.asStateFlow()
    
    // 消息监听器
    private var messageListener: WebSocketMessageListener? = null
    
    /**
     * 连接WebSocket并进行认证
     */
    fun connect(authToken: String, clientInfo: ClientInfo) {
        if (_connectionState.value == WebSocketState.CONNECTING || 
            _connectionState.value == WebSocketState.AUTHENTICATED) {
            Log.d(TAG, "WebSocket已连接或正在连接中")
            return
        }
        
        Log.d(TAG, "开始连接WebSocket")
        _connectionState.value = WebSocketState.CONNECTING
        
        val request = Request.Builder()
            .url(WS_URL)
            .build()
        
        webSocket = client.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                Log.d(TAG, "WebSocket连接已建立")
                _connectionState.value = WebSocketState.CONNECTED
                reconnectAttempts = 0
                
                // 发送认证消息
                val authMessage = AuthMessage(
                    authToken = authToken,
                    clientInfo = clientInfo
                )
                sendMessage(authMessage)
            }
            
            override fun onMessage(webSocket: WebSocket, text: String) {
                Log.d(TAG, "收到WebSocket消息: $text")
                handleMessage(text)
            }
            
            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket连接正在关闭: $code - $reason")
                _connectionState.value = WebSocketState.DISCONNECTED
            }
            
            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "WebSocket连接已关闭: $code - $reason")
                _connectionState.value = WebSocketState.DISCONNECTED
                stopPing()
            }
            
            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                Log.e(TAG, "WebSocket连接失败", t)
                _connectionState.value = WebSocketState.ERROR
                stopPing()
                
                // 尝试重连
                if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                    scheduleReconnect(authToken, clientInfo)
                } else {
                    Log.e(TAG, "达到最大重连次数，停止重连")
                }
            }
        })
    }
    
    /**
     * 断开WebSocket连接
     */
    fun disconnect() {
        Log.d(TAG, "断开WebSocket连接")
        
        stopPing()
        cancelReconnect()
        
        webSocket?.close(1000, "正常关闭")
        webSocket = null
        
        _connectionState.value = WebSocketState.DISCONNECTED
    }
    
    /**
     * 发送消息
     */
    fun sendMessage(message: WebSocketMessage): Boolean {
        return try {
            val json = gson.toJson(message)
            val success = webSocket?.send(json) ?: false
            if (success) {
                Log.d(TAG, "发送消息: ${message.type}")
            } else {
                Log.w(TAG, "发送消息失败: ${message.type}")
            }
            success
        } catch (e: Exception) {
            Log.e(TAG, "发送消息异常", e)
            false
        }
    }
    
    /**
     * 设置消息监听器
     */
    fun setMessageListener(listener: WebSocketMessageListener) {
        this.messageListener = listener
    }
    
    /**
     * 处理收到的消息
     */
    private fun handleMessage(text: String) {
        try {
            // 先解析消息类型
            val messageType = gson.fromJson(text, MessageTypeWrapper::class.java)?.type
            
            when (messageType) {
                "auth_success" -> {
                    val message = gson.fromJson(text, AuthSuccessMessage::class.java)
                    _connectionState.value = WebSocketState.AUTHENTICATED
                    startPing()
                    messageListener?.onAuthSuccess(message)
                }
                "auth_failed" -> {
                    val message = gson.fromJson(text, AuthFailedMessage::class.java)
                    _connectionState.value = WebSocketState.ERROR
                    messageListener?.onAuthFailed(message)
                }
                "cloud_config" -> {
                    val message = gson.fromJson(text, CloudConfigMessage::class.java)
                    messageListener?.onCloudConfig(message)
                }
                "sync_success" -> {
                    val message = gson.fromJson(text, SyncSuccessMessage::class.java)
                    messageListener?.onSyncSuccess(message)
                }
                "sync_failed" -> {
                    val message = gson.fromJson(text, SyncFailedMessage::class.java)
                    messageListener?.onSyncFailed(message)
                }
                "config_updated" -> {
                    val message = gson.fromJson(text, ConfigUpdatedMessage::class.java)
                    messageListener?.onConfigUpdated(message)
                }
                "pong" -> {
                    // 心跳响应，无需处理
                    Log.d(TAG, "收到心跳响应")
                }
                "error" -> {
                    val message = gson.fromJson(text, ErrorMessage::class.java)
                    messageListener?.onError(message)
                }
                else -> {
                    Log.w(TAG, "未知消息类型: $messageType")
                }
            }
        } catch (e: JsonSyntaxException) {
            Log.e(TAG, "解析消息失败: $text", e)
        } catch (e: Exception) {
            Log.e(TAG, "处理消息异常", e)
        }
    }
    
    /**
     * 开始心跳
     */
    private fun startPing() {
        stopPing()
        pingJob = scope.launch {
            while (isActive && _connectionState.value == WebSocketState.AUTHENTICATED) {
                delay(PING_INTERVAL)
                if (_connectionState.value == WebSocketState.AUTHENTICATED) {
                    sendMessage(PingMessage())
                }
            }
        }
    }
    
    /**
     * 停止心跳
     */
    private fun stopPing() {
        pingJob?.cancel()
        pingJob = null
    }
    
    /**
     * 安排重连
     */
    private fun scheduleReconnect(authToken: String, clientInfo: ClientInfo) {
        cancelReconnect()
        reconnectAttempts++
        
        Log.d(TAG, "安排重连，第 $reconnectAttempts 次尝试")
        _connectionState.value = WebSocketState.RECONNECTING
        
        reconnectJob = scope.launch {
            delay(RECONNECT_DELAY)
            if (isActive) {
                connect(authToken, clientInfo)
            }
        }
    }
    
    /**
     * 取消重连
     */
    private fun cancelReconnect() {
        reconnectJob?.cancel()
        reconnectJob = null
    }
    
    /**
     * 消息类型包装器，用于解析消息类型
     */
    private data class MessageTypeWrapper(val type: String)
}

/**
 * WebSocket 消息监听器接口
 */
interface WebSocketMessageListener {
    fun onAuthSuccess(message: AuthSuccessMessage)
    fun onAuthFailed(message: AuthFailedMessage)
    fun onCloudConfig(message: CloudConfigMessage)
    fun onSyncSuccess(message: SyncSuccessMessage)
    fun onSyncFailed(message: SyncFailedMessage)
    fun onConfigUpdated(message: ConfigUpdatedMessage)
    fun onError(message: ErrorMessage)
}
